<template>
  <div class="flex h-full flex-col overflow-hidden">
    <h2 class="bg-w flex h-[60px] items-center pl-5 text-xl font-semibold">数据集管理</h2>

    <!-- 主体内容区 -->
    <div v-loading="loading" class="bg-w m-5 flex h-0 flex-1 flex-col rounded-md pt-5">
      <!-- 操作栏 -->
      <div class="flex justify-between px-10">
        <el-button type="primary" @click="onAdd">新增</el-button>
        <!-- <el-input
          v-model="search"
          placeholder="请输入数据集说明搜索"
          style="width: 300px"
          clearable
          @clear="onSearch"
          @keyup.enter="onSearch"
        >
          <template #append>
            <el-button :icon="Search" @click="onSearch" />
          </template>
        </el-input> -->
      </div>

      <!-- 表格区域 -->
      <div class="mt-3 h-0 w-full flex-1 px-10">
        <el-table height="100%" :data="tableData" style="width: 100%" class="c-table-header">
          <el-table-column prop="datasetNameCn" min-width="100px" label="数据集名称(中文)" show-overflow-tooltip />
          <el-table-column prop="datasetName" min-width="100px" label="数据集名称(英文)" show-overflow-tooltip />
          <el-table-column prop="projectCode" label="课题编码缩写" />
          <el-table-column prop="description" label="数据集说明" show-overflow-tooltip />
          <el-table-column prop="diseaseType" label="初始疾病类型" />
          <el-table-column prop="createDate" label="更新日期" width="170px" />
          <el-table-column prop="projectLeader" label="项目负责人" />
          <el-table-column prop="affiliatedUnit" label="所属单位" />
          <el-table-column prop="state" label="状态" />
          <el-table-column fixed="right" label="操作" width="200px">
            <template #default="{ row }">
              <div class="flex flex-wrap justify-center gap-y-2">
                <el-button link type="primary" @click="onViewDetail(row)">查看</el-button>
                <el-button v-if="row.state !== '废弃'" link type="primary" @click="onEdit(row)">修改</el-button>
                <el-popconfirm v-if="row.state !== '废弃'" title="确定停用？" @confirm="onDel(row)">
                  <template #reference>
                    <el-button link type="primary">停用</el-button>
                  </template>
                </el-popconfirm>
                <el-button v-if="row.state !== '废弃'" link type="primary" @click="onImport(row)">上传数据集</el-button>
                <el-button link type="primary" @click="onHistory(row)"> 历史数据集 </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-bottom">
        <el-pagination
          background
          layout="total, prev, pager, next, jumper"
          :page-size="pagination.pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>

  <!-- 新增/编辑弹窗 -->
  <el-dialog
    v-model="showAdd"
    :title="formTitle"
    width="600px"
    destroy-on-close
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="onAddClose"
  >
    <div v-if="uploadLoading" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in logMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
      <div class="error-summary" v-if="hasErrors">
        <div class="error-title">
          <el-icon><WarningFilled /></el-icon> 错误信息汇总
        </div>
        <div class="error-list">
          <p v-for="(err, index) in errorMessages" :key="index">{{ err }}</p>
        </div>
      </div>
    </div>
    <DatasetForm
      v-else
      ref="datasetFormRef"
      show-database
      :show-metadata="showMetadata"
      @change-show-meatadata="(e) => (showMetadata = e)"
    />
    <template #footer>
      <span>
        <el-button @click="onAddClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onAddConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 上传数据集弹窗 -->
  <el-dialog
    v-model="showDatabase"
    title="上传数据集"
    width="600px"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :before-close="handleDatabaseBeforeClose"
    @close="onDatabaseClose"
  >
    <div v-show="!startUpload">
      <el-form ref="dbFormRef" label-position="top" :model="dbForm" :rules="dbRules">
        <el-form-item label="数据集文件" prop="dbset">
          <el-upload v-model:file-list="dbForm.dbset" accept=".xls,.xlsx" :limit="1" :auto-upload="false">
            <el-button type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="startUpload" class="log-container">
      <div class="log-messages">
        <p v-for="(msg, index) in logMessages" :key="index" :class="{ 'error-message': msg.isError }">
          {{ msg.text }}
        </p>
      </div>
      <div class="error-summary" v-if="hasErrors">
        <div class="error-title">
          <el-icon><WarningFilled /></el-icon> 错误信息汇总
        </div>
        <div class="error-list">
          <p v-for="(err, index) in errorMessages" :key="index">{{ err }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <span>
        <el-button :disabled="isDatasetUploading" @click="onDatabaseClose">取消</el-button>
        <el-button type="primary" :loading="addLoading" @click="onDatabaseConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 历史数据集弹窗 -->
  <HistoryDatasetDialog v-if="showHistory" v-model="showHistory" :current-row="currentRow!" />

  <VerifyDialog :id="verifyId" v-model="showVerify" @success="onVerifySuccess" />
</template>

<script setup lang="ts">
  // 导入所需的依赖
  import {
    findFileInforByUserId,
    deleteEntityById_36,
    findAllDb,
    newOrUpdateEntity_10,
    isFileInforProcessing,
    getFileMessages_1_02,
  } from '@/api';
  import { WarningFilled } from '@element-plus/icons-vue';
  import { ElMessage, FormInstance, UploadFiles } from 'element-plus';
  import { useRouter } from 'vue-router';
  import DatasetForm from '../components/DatasetForm.vue';
  import VerifyDialog from '../components/VerifyDialog.vue';
  import dayjs from 'dayjs';
  import HistoryDatasetDialog from '../components/HistoryDatasetDialog.vue';
  import { useUsers } from '@/store/index';
  import { upload } from '@/utils/request';
  const store = useUsers();

  // 基础数据
  const router = useRouter();
  const loading = ref(false);
  const currentRow = ref<FileInfoVO | null>(null);
  const importText = ref('');

  // 日志和错误处理
  interface LogMessage {
    text: string;
    isError: boolean;
  }
  const logMessages = ref<LogMessage[]>([]);
  const errorMessages = ref<string[]>([]);
  const hasErrors = computed(() => errorMessages.value.length > 0);

  // 表格数据
  const tableData = ref<FileInfoVO[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
  });
  const total = ref(0);

  // 新增/编辑表单相关
  const datasetFormRef = ref<any>(null);
  const showMetadata = ref(true);
  const showAdd = ref(false);
  const addLoading = ref(false);

  const formTitle = computed(() => {
    if (datasetFormRef.value) {
      return datasetFormRef.value.addForm.id ? '编辑数据集' : '新增数据集';
    }
    return '';
  });

  // 上传数据集相关
  const uploadLoading = ref(false);
  const showDatabase = ref(false);
  const databaseValue = ref(0);
  const databaseList = ref<CBDDefDatabaseVO[]>([]);
  const startUpload = ref(false);
  const dbFormRef = ref<FormInstance>();
  const dbForm = reactive({
    dbset: [] as UploadFiles,
  });
  const dbRules = ref({
    dbset: [{ required: true, message: '不能为空' }],
  });
  const isDatasetUploading = ref(false);

  // 校验数据相关
  const showVerify = ref(false);
  const verifyId = ref(0);

  // 上传任务查询和进度相关
  const uploadTaskPolling = ref<NodeJS.Timeout | null>(null);

  // 已显示的消息集合，用于去重
  const displayedMessages = ref<Set<string>>(new Set());

  // 生成消息的唯一标识
  const generateMessageId = (msg: any) => {
    return `${msg.timestamp || ''}_${msg.msg || ''}_${msg.code || ''}`;
  };

  // 处理并显示新消息
  const processAndDisplayMessages = (messages: any[]) => {
    if (!messages || messages.length === 0) return;

    // 按时间戳排序消息（如果有的话）
    const sortedMessages = [...messages].sort((a, b) => {
      const timeA = a.timestamp || '';
      const timeB = b.timestamp || '';
      return timeA.localeCompare(timeB);
    });

    sortedMessages.forEach((msg) => {
      const messageId = generateMessageId(msg);
      if (!displayedMessages.value.has(messageId)) {
        const messageText = msg.msg || '处理中...';
        const timestamp = msg.timestamp || '处理中...';

        if (msg.code === 1) {
          // 错误消息，添加到错误信息汇总
          addLogMessage(`[${timestamp}] ${messageText}`);
          if (!errorMessages.value.includes(messageText)) {
            errorMessages.value.push(messageText);
          }
        } else {
          // 正常消息、开始消息、结束消息等
          addLogMessage(`[${timestamp}] ${messageText}`);
        }

        // 标记消息已显示
        displayedMessages.value.add(messageId);
      }
    });
  };

  // 添加日志消息的方法
  const addLogMessage = (text: string) => {
    const isError = text.includes('错误提示') || text.includes('失败');
    logMessages.value.push({ text, isError });

    // 如果是错误消息，添加到错误消息列表
    if (isError) {
      errorMessages.value.push(text);
    }

    // 保持滚动到最新消息
    nextTick(() => {
      const logContainer = document.querySelector('.log-messages');
      if (logContainer) {
        logContainer.scrollTop = logContainer.scrollHeight;
      }
    });
  };

  // 重置日志和错误信息
  const resetLogs = () => {
    logMessages.value = [];
    errorMessages.value = [];
    importText.value = '';
    displayedMessages.value.clear();
  };

  // 查询是否有正在进行的数据集上传任务
  const checkDatasetUploadTask = async (projectCode: string) => {
    try {
      const params = {
        projectCode,
      };
      const { data } = await isFileInforProcessing(params);
      return data;
    } catch (error) {
      console.error('查询数据集上传任务失败:', error);
      return false;
    }
  };

  // 查询是否有正在进行的元数据上传任务
  const checkMetadataUploadTask = async (projectCode: string) => {
    try {
      const params = {
        projectCode,
      };
      const { data } = await isFileInforProcessing(params);
      console.log('🚀 ~ checkMetadataUploadTask ~ data:', data);
      return data;
    } catch (error) {
      console.error('查询元数据上传任务失败:', error);
      return false;
    }
  };

  // 获取数据集上传进度信息
  const getDatasetUploadProgress = async (projectCode: string) => {
    try {
      const params = {
        projectCode,
      };
      const { data } = await getFileMessages_1_02(params);
      return data;
    } catch (error) {
      console.error('获取数据集上传进度失败:', error);
      return null;
    }
  };

  // 获取元数据上传进度信息
  const getMetadataUploadProgress = async (projectCode: string) => {
    try {
      const params = {
        projectCode,
      };
      const { data } = await getFileMessages_1_02(params);
      return data;
    } catch (error) {
      console.error('获取元数据上传进度失败:', error);
      return null;
    }
  };

  // 开始轮询数据集上传进度
  const startDatasetUploadProgressPolling = (projectCode: string) => {
    if (uploadTaskPolling.value) {
      clearInterval(uploadTaskPolling.value);
    }

    let pollCount = 0;
    const maxPollCount = 150; // 最多轮询5分钟 (150 * 2秒)

    uploadTaskPolling.value = setInterval(async () => {
      pollCount++;

      // 防止无限轮询
      if (pollCount > maxPollCount) {
        stopUploadProgressPolling();
        addLogMessage('上传任务超时，请手动刷新页面查看结果');
        return;
      }

      const progressData = await getDatasetUploadProgress(projectCode);
      if (progressData && progressData.length > 0) {
        // 使用新的消息处理函数
        processAndDisplayMessages(progressData);
      } else if (displayedMessages.value.size === 0) {
        // 第一次查询且没有数据，显示提示
        addLogMessage('正在处理中，暂无进度信息...');
        displayedMessages.value.add('initial_message'); // 标记已显示过提示，避免重复显示
      }

      // 检查是否完成（只有当有数据时才检查）
      if (progressData && progressData.length > 0) {
        const hasCompleted = progressData.some((msg: any) => msg.code === 4); // code=4是上传结束

        if (hasCompleted) {
          stopUploadProgressPolling();
          addLoading.value = false;
          addLogMessage('数据集上传完成');
          ElMessage({ type: 'success', message: '数据集上传完成' });
          // 设置上传完成状态，让确定按钮知道应该关闭弹窗
          uploadLoading.value = true;
          fetchData();
        }
      }
    }, 2000); // 每2秒查询一次
  };

  // 开始轮询元数据上传进度
  const startMetadataUploadProgressPolling = (projectCode: string) => {
    if (uploadTaskPolling.value) {
      clearInterval(uploadTaskPolling.value);
    }

    let pollCount = 0;
    const maxPollCount = 150; // 最多轮询5分钟 (150 * 2秒)

    uploadTaskPolling.value = setInterval(async () => {
      pollCount++;

      // 防止无限轮询
      if (pollCount > maxPollCount) {
        stopUploadProgressPolling();
        addLogMessage('上传任务超时，请手动刷新页面查看结果');
        return;
      }

      const progressData = await getMetadataUploadProgress(projectCode);
      if (progressData && progressData.length > 0) {
        // 使用新的消息处理函数
        processAndDisplayMessages(progressData);
      } else if (displayedMessages.value.size === 0) {
        // 第一次查询且没有数据，显示提示
        addLogMessage('正在处理中，暂无进度信息...');
        displayedMessages.value.add('initial_message'); // 标记已显示过提示，避免重复显示
      }

      // 检查是否完成（只有当有数据时才检查）
      if (progressData && progressData.length > 0) {
        const hasCompleted = progressData.some((msg: any) => msg.code === 4); // code=4是上传结束

        if (hasCompleted) {
          stopUploadProgressPolling();
          addLoading.value = false;
          addLogMessage('元数据上传完成');
          ElMessage({ type: 'success', message: '元数据上传完成' });
          fetchData();
        }
      }
    }, 2000);
  };

  // 停止轮询上传进度
  const stopUploadProgressPolling = () => {
    if (uploadTaskPolling.value) {
      clearInterval(uploadTaskPolling.value);
      uploadTaskPolling.value = null;
    }
  };

  // 方法定义
  const fetchData = async (pageNum = 1) => {
    try {
      loading.value = true;
      const { data } = await findFileInforByUserId(store.user.id, pageNum, pagination.pageSize);
      total.value = data!.totalElement!;
      tableData.value = data?.content || [];
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchData(page);
  };

  const onAdd = () => {
    resetLogs();
    showMetadata.value = true;
    uploadLoading.value = false; // 重置上传状态，确保显示表单而不是日志
    showAdd.value = true;
  };

  const onEdit = async (row: FileInfoVO) => {
    resetLogs();
    showMetadata.value = false;
    showAdd.value = true;
    nextTick(() => {
      if (row) {
        Object.keys(datasetFormRef.value.addForm).forEach((key) => {
          if (datasetFormRef.value.addForm[key] !== null && datasetFormRef.value.addForm[key] !== undefined) {
            datasetFormRef.value.addForm[key] = row[key];
          }
        });
      }
    });
  };

  const onAddClose = () => {
    stopUploadProgressPolling();
    addLoading.value = false;
    showAdd.value = false;
    datasetFormRef.value?.formRef?.resetFields();
    uploadLoading.value = false;
    resetLogs();
  };

  const onAddConfirm = async () => {
    if (uploadLoading.value) {
      onAddClose();
      return;
    }

    if (!datasetFormRef.value.formRef) {
      ElMessage({ type: 'warning', message: '请先导入元数据' });
      return;
    }

    const valid = await datasetFormRef.value.formRef.validate();
    if (!valid) return;

    try {
      addLoading.value = true;
      resetLogs();

      if (!datasetFormRef.value.addForm.id) {
        // 新增 - 先检查是否有正在进行的元数据上传任务
        addLogMessage('正在检查元数据上传任务状态...');
        const projectCode = datasetFormRef.value.addForm.projectCode;
        if (!projectCode) {
          ElMessage({ type: 'error', message: '请先填写课题编码缩写' });
          return;
        }
        const hasRunningTask = await checkMetadataUploadTask(projectCode);
        if (hasRunningTask) {
          addLogMessage('检测到正在进行的元数据上传任务，继续显示进度...');
          // 切换到显示上传进度状态
          uploadLoading.value = true;
          addLoading.value = true; // 设置loading状态
          const progressData = await getMetadataUploadProgress(projectCode);
          if (progressData && progressData.length > 0) {
            // 使用新的消息处理函数显示现有的进度消息
            processAndDisplayMessages(progressData);
          } else {
            if (!displayedMessages.value.has('initial_message')) {
              addLogMessage('正在处理中，暂无进度信息...');
              displayedMessages.value.add('initial_message');
            }
          }
          // 不管是否有进度信息，都开始轮询
          startMetadataUploadProgressPolling(projectCode);
          return;
        }

        uploadLoading.value = true;

        const databaseId = datasetFormRef.value.addForm.databaseId;

        addLogMessage('开始上传元数据...');
        const formData = new FormData();
        // 将参数对象转换为JSON字符串并作为文件上传
        const paramsBlob = new Blob([JSON.stringify(datasetFormRef.value.addForm)], { type: 'application/json' });
        formData.append('fileInforDTO', paramsBlob, 'fileInforDTO.json');

        // 不等待upload完成，直接开始上传和查询进度
        upload(`/FileInfor/uploadFileId/hasmddExt?userIdCur=${store.user.id}&cbdDatabaseId=${databaseId}`, {
          method: 'post',
          data: formData,
        }).catch((error) => {
          console.error('上传元数据失败:', error);
          addLogMessage('上传元数据失败: ' + (error.message || '未知错误'));
          // 停止轮询
          stopUploadProgressPolling();
          addLoading.value = false;
          uploadLoading.value = false;
        });

        // 立即开始轮询元数据上传进度
        startMetadataUploadProgressPolling(projectCode);
      } else {
        // 编辑
        const form = {
          ...datasetFormRef.value.addForm,
          createDate: dayjs(datasetFormRef.value.addForm.createDate).format('YYYY-MM-DD'),
        };
        await newOrUpdateEntity_10(form);
        onAddClose();
        addLoading.value = false;
        ElMessage({ type: 'success', message: '操作成功' });
        fetchData();
      }
    } catch (error) {
      console.error(error);
      ElMessage({ type: 'error', message: '操作失败，请重试' });
      addLoading.value = false;
    }
  };

  const onViewDetail = (row: FileInfoVO) => {
    router.push({ name: 'DatasetField', params: { id: row.id } });
  };

  const onDel = async (row: FileInfoVO) => {
    try {
      loading.value = true;
      await deleteEntityById_36(row.id!);
      ElMessage({ type: 'success', message: '删除成功' });
      fetchData();
    } catch (error) {
      console.error(error);
    } finally {
      loading.value = false;
    }
  };

  const onVerifySuccess = () => {
    fetchDatabase();
    databaseValue.value = 0;
    showDatabase.value = true;
  };

  const onImport = async (row: FileInfoVO) => {
    resetLogs();
    currentRow.value = row;

    // 检查是否有正在进行的数据集上传任务
    const projectCode = row.projectCode;
    if (!projectCode) {
      ElMessage({ type: 'error', message: '当前数据集缺少课题编码信息' });
      return;
    }

    addLogMessage('正在检查数据集上传任务状态...');
    const hasRunningTask = await checkDatasetUploadTask(projectCode);
    if (hasRunningTask) {
      addLogMessage('检测到正在进行的数据集上传任务，显示进度...');
      // 切换到显示上传进度状态
      startUpload.value = true;
      addLoading.value = true; // 设置loading状态
      const progressData = await getDatasetUploadProgress(projectCode);
      if (progressData && progressData.length > 0) {
        // 使用新的消息处理函数显示现有的进度消息
        processAndDisplayMessages(progressData);
      } else {
        if (!displayedMessages.value.has('initial_message')) {
          addLogMessage('正在处理中，暂无进度信息...');
          displayedMessages.value.add('initial_message');
        }
      }
      // 不管是否有进度信息，都开始轮询
      startDatasetUploadProgressPolling(projectCode);
    } else {
      addLogMessage('没有检测到正在进行的上传任务');
    }

    showDatabase.value = true;
  };

  const showHistory = ref(false);
  const onHistory = (row: FileInfoVO) => {
    currentRow.value = row;
    showHistory.value = true;
  };

  const handleDatabaseBeforeClose = (done: () => void) => {
    //上传链接建立中，不允许关闭弹窗
    if (isDatasetUploading.value) {
      ElMessage({ type: 'warning', message: '上传链接建立中，请稍候...' });
      return;
    }
    done();
  };

  const onDatabaseClose = () => {
    //上传链接建立中，不允许关闭弹窗
    if (isDatasetUploading.value) return;

    stopUploadProgressPolling();
    startUpload.value = false;
    dbFormRef.value?.resetFields();
    showDatabase.value = false;
    addLoading.value = false;
    resetLogs();
  };

  const fetchDatabase = async () => {
    try {
      const { data } = await findAllDb();
      databaseList.value = data!;
    } catch (error) {
      console.error(error);
    }
  };

  const onDatabaseConfirm = async () => {
    if (uploadLoading.value || startUpload.value) {
      onDatabaseClose();
      return;
    }

    const valid = await dbFormRef.value?.validate();
    if (!valid) return;

    try {
      addLoading.value = true;
      startUpload.value = true;
      resetLogs();

      const projectCode = currentRow.value?.projectCode;
      if (!projectCode) {
        ElMessage({ type: 'error', message: '当前数据集缺少课题编码信息' });
        return;
      }

      if (!currentRow.value?.id) {
        throw new Error('未选择数据集');
      }

      addLogMessage('开始上传数据集...');

      // 切换到显示上传进度状态
      startUpload.value = true;
      addLoading.value = true; // 设置loading状态
      isDatasetUploading.value = true;

      const formData = new FormData();
      formData.append('datasetFile', dbForm.dbset[0].raw!);
      //上传数据集等待完成才开始查询进度
      await upload(
        `/FileInfor/exportMedicalDataSetToDatabaseExt?userIdCur=${store.user.id}&fileId=${currentRow.value.id}`,
        {
          method: 'post',
          data: formData,
        }
      );

      // 立即开始轮询数据集上传进度
      startDatasetUploadProgressPolling(projectCode);
    } catch (error: any) {
      console.error('上传数据集失败:', error);
      addLogMessage('上传数据集失败: ' + (error.message || '未知错误'));
      addLoading.value = false;
      startUpload.value = false;
      ElMessage({ type: 'error', message: '上传失败，请重试' });
    } finally {
      isDatasetUploading.value = false;
    }
  };

  // 生命周期钩子
  onBeforeMount(() => {
    fetchData();
  });

  onUnmounted(() => {
    stopUploadProgressPolling();
    startUpload.value = false;
  });
</script>

<style lang="scss" scoped>
  .vertical-radio-group {
    display: block;
    .el-radio {
      display: block;
    }
  }

  .log-container {
    display: flex;
    flex-direction: column;
    height: 400px;
    width: 100%;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;
  }

  .log-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    font-family: monospace;
    background-color: #f8f9fa;
    line-height: 1.5;
    font-size: 14px;
  }

  .error-message {
    color: #f56c6c;
    font-weight: 500;
  }

  .error-summary {
    margin-top: 10px;
    padding: 10px;
    background-color: #fff0f0;
    border-top: 1px solid #fde2e2;
  }

  .error-title {
    display: flex;
    align-items: center;
    color: #f56c6c;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 8px;

    .el-icon {
      margin-right: 6px;
    }
  }

  .error-list {
    max-height: 100px;
    overflow-y: auto;

    p {
      margin: 4px 0;
      padding-left: 16px;
      font-size: 13px;
      color: #f56c6c;
    }
  }

  .upload-progress-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .progress-content {
    .queue-progress {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 15px;

      .queue-title {
        margin: 0 0 10px 0;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #ebeef5;
        padding-bottom: 8px;
      }

      .messages-list {
        .message-item {
          margin-bottom: 10px;
          padding: 8px;
          background-color: #f8f9fa;
          border-radius: 4px;

          .message-time {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }

          .message-content {
            font-size: 14px;
            color: #303133;
            margin-bottom: 8px;
          }

          .message-progress {
            margin-top: 8px;
          }
        }
      }
    }
  }

  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    font-size: 14px;
    color: #606266;

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
</style>
